/**
  ******************************************************************************
  * @file    bsp_flash.h
  * @brief   FLASH read/write operations wrapper
  ******************************************************************************
  */

#ifndef __BSP_FLASH_H
#define __BSP_FLASH_H

#ifdef __cplusplus
 extern "C" {
#endif

#include "main.h"

// FLASH storage start address
// STM32L071: 128KB Flash, 1024 pages, 128 bytes per page
// Use the last page (page 1023) for user data storage
#define FLASH_USER_START_ADDR   (FLASH_BASE + (FLASH_PAGE_SIZE * 1023))
#define FLASH_USER_END_ADDR     (FLASH_USER_START_ADDR + FLASH_PAGE_SIZE)

// Data item size definition
#define FLASH_DATA_ITEM_SIZE    8

// Data type definitions
#define FLASH_DATA_TYPE_UINT8   0
#define FLASH_DATA_TYPE_UINT16  1
#define FLASH_DATA_TYPE_UINT32  2
#define FLASH_DATA_TYPE_FLOAT   3

// Maximum available data index
#define FLASH_MAX_DATA_INDEX    15

// Initialize FLASH
HAL_StatusTypeDef FLASH_Init(void);

// Write data to FLASH
HAL_StatusTypeDef FLASH_WriteData(uint8_t *pData, uint16_t DataSize);

// Read data from FLASH
HAL_StatusTypeDef FLASH_ReadData(uint8_t *pData, uint16_t DataSize);

// Erase FLASH user area
HAL_StatusTypeDef FLASH_EraseUserArea(void);

// Write single data item to FLASH
HAL_StatusTypeDef Flash_Write(uint8_t index, uint8_t dataType, uint32_t data);

// Read single data item from FLASH
HAL_StatusTypeDef Flash_Read(uint8_t index, uint8_t dataType, void *pData);

// Write uint32_t data to FLASH
HAL_StatusTypeDef Flash_WriteUint32(uint8_t index, uint32_t value);

// Read uint32_t data from FLASH
HAL_StatusTypeDef Flash_ReadUint32(uint8_t index, uint32_t *pValue);

#ifdef __cplusplus
}
#endif

#endif /* __BSP_FLASH_H */
