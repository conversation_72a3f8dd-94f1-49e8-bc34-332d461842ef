/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    GSM.c
  * @brief   GSM模块AT指令控制实现
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "GSM.h"
#include "gpio.h"
#include "globals.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/* 私有变量定义 */
static GSM_State_t gsm_state = GSM_STATE_INIT;
static char gsm_rx_buffer[GSM_RX_BUFFER_SIZE];  // 接收缓冲区

/* 私有函数声明 */
static void GSM_PrintError(const char* command);
static uint16_t GSM_ReceiveData(uint32_t timeout);
static void GSM_PrintResponse(const char* command, const char* response);

/* USER CODE BEGIN 1 */

/**
 * @brief  GSM电源开启
 */
void GSM_PowerOn(void)
{
    RF_PWR_ON;  // 使用gpio.h中定义的宏
}

/**
 * @brief  GSM电源关闭
 */
void GSM_PowerOff(void)
{
    RF_PWR_OFF;  // 使用gpio.h中定义的宏
}

/**
 * @brief  GSM完整初始化流程（包含电源控制和等待）
 * @retval GSM_Status_t 初始化状态
 */
GSM_Status_t GSM_FullInit(void)
{
    // 开启GSM电源
    GSM_PowerOn();

    // 等待GSM模块启动（至少5秒）
    HAL_Delay(GSM_POWER_ON_DELAY_MS);

    // 执行GSM初始化
    GSM_Status_t status = GSM_Init();

    return status;
}

/* USER CODE END 1 */

/**
 * @brief  GSM模块初始化
 * @retval GSM_Status_t 初始化状态
 */
GSM_Status_t GSM_Init(void)
{
    GSM_Status_t status;

    // 重置接收缓冲区
    memset(gsm_rx_buffer, 0, sizeof(gsm_rx_buffer));

    // 1. 关闭回显
    status = GSM_SendATCommandWithRetry("ATE0", "OK", GSM_AT_TIMEOUT_MS, GSM_AT_RETRY_COUNT);
    if (status != GSM_OK) {
        gsm_state = GSM_STATE_ERROR;
        return status;
    }

    // 2. 获取CCID号并保存到全局变量
    status = GSM_SendATCommandWithRetry("AT+CCID", "OK", GSM_AT_TIMEOUT_MS, GSM_AT_RETRY_COUNT);
    if (status == GSM_OK) {
        // 解析CCID并保存到全局变量
        // 查找数字开头的CCID（通常是20位数字）
        char* ptr = gsm_rx_buffer;
        while (*ptr) {
            if (*ptr >= '0' && *ptr <= '9') {
                // 找到数字开头，提取CCID
                char* start = ptr;
                while (*ptr >= '0' && *ptr <= '9') {
                    ptr++;
                }
                uint16_t len = ptr - start;
                if (len >= 15 && len <= 20 && len < sizeof(gsm_ccid)) {  // CCID通常15-20位
                    strncpy(gsm_ccid, start, len);
                    gsm_ccid[len] = '\0';
                    break;
                }
            }
            ptr++;
        }
    }

    // 3. 获取模块电压
    uint16_t voltage = 0;
    GSM_GetVoltage(&voltage);

    // 4. 查询网络注册状态
    uint8_t network_reg = 0;
    GSM_CheckNetwork(&network_reg);

    // 5. 获取信号强度并保存到全局变量
    uint8_t signal = 0;
    status = GSM_GetSignal(&signal);
    if (status == GSM_OK) {
        gsm_signal_quality = (int8_t)signal;
    } else {
        gsm_signal_quality = -128;  // 表示无效
    }

    gsm_state = GSM_STATE_READY;
    return GSM_OK;
}

/**
 * @brief  关闭回显
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_CloseEcho(void)
{
    return GSM_SendATCommand("ATE0", "OK", GSM_AT_TIMEOUT_MS);
}

/**
 * @brief  获取模块型号
 * @param  model: 型号字符串缓冲区
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_GetModel(char* model)
{
    GSM_Status_t status = GSM_SendATCommand("AT+CGMM", "OK", GSM_AT_TIMEOUT_MS);
    if (status == GSM_OK) {
        // 解析响应中的型号信息
        char* start = strstr(gsm_rx_buffer, "+CGMM: ");
        if (start) {
            start += 7;  // 跳过"+CGMM: "
            char* end = strchr(start, '\r');
            if (end) {
                uint16_t len = end - start;
                if (len > 0) {
                    strncpy(model, start, len);
                    model[len] = '\0';
                }
            }
        }
    }
    return status;
}

/**
 * @brief  获取CCID号
 * @param  ccid: CCID字符串缓冲区
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_GetCCID(char* ccid)
{
    GSM_Status_t status = GSM_SendATCommand("AT+CCID", "OK", GSM_AT_TIMEOUT_MS);
    if (status == GSM_OK) {
        // 解析响应中的CCID信息
        char* start = strstr(gsm_rx_buffer, "AT+CCID");
        if (start) {
            start = strchr(start, '\r');
            if (start) {
                start = strchr(start + 1, '\r');
                if (start) {
                    start += 2;  // 跳过"\r\n"
                    char* end = strchr(start, '\r');
                    if (end) {
                        uint16_t len = end - start;
                        if (len > 0 && len < 32) {
                            strncpy(ccid, start, len);
                            ccid[len] = '\0';
                        }
                    }
                }
            }
        }
    }
    return status;
}

/**
 * @brief  获取模块电压
 * @param  voltage: 电压值指针(mV)
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_GetVoltage(uint16_t* voltage)
{
    GSM_Status_t status = GSM_SendATCommand("AT+CBC", "OK", GSM_AT_TIMEOUT_MS);
    if (status == GSM_OK) {
        // 解析响应中的电压信息
        char* start = strstr(gsm_rx_buffer, "+CBC: ");
        if (start) {
            start += 6;  // 跳过"+CBC: "
            *voltage = (uint16_t)atoi(start);
        }
    }
    return status;
}

/**
 * @brief  查询网络注册状态
 * @param  reg_status: 注册状态指针
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_CheckNetwork(uint8_t* reg_status)
{
    GSM_Status_t status = GSM_SendATCommand("AT+CREG?", "OK", GSM_AT_TIMEOUT_MS);
    if (status == GSM_OK) {
        // 解析响应中的注册状态
        char* start = strstr(gsm_rx_buffer, "+CREG: ");
        if (start) {
            start += 7;  // 跳过"+CREG: "
            char* comma = strchr(start, ',');
            if (comma) {
                comma++;  // 跳过逗号
                *reg_status = (uint8_t)atoi(comma);
            }
        }
    }
    return status;
}

/**
 * @brief  获取信号强度
 * @param  signal: 信号强度指针(0-31)
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_GetSignal(uint8_t* signal)
{
    GSM_Status_t status = GSM_SendATCommand("AT+CSQ", "OK", GSM_AT_TIMEOUT_MS);
    if (status == GSM_OK) {
        // 解析响应中的信号强度
        char* start = strstr(gsm_rx_buffer, "+CSQ: ");
        if (start) {
            start += 6;  // 跳过"+CSQ: "
            char* comma = strchr(start, ',');
            if (comma) {
                *comma = '\0';  // 临时结束符
                *signal = (uint8_t)atoi(start);
            }
        }
    }
    return status;
}

/**
 * @brief  连接TCP服务器
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_ConnectServer(void)
{
    char command[128];
    sprintf(command, "AT+CIPSTART=\"TCP\",\"%s\",%s", GSM_SERVER_IP, GSM_SERVER_PORT);

    // 清空接收缓冲区
    memset(gsm_rx_buffer, 0, sizeof(gsm_rx_buffer));

    // 发送AT指令
    HAL_UART_Transmit(&hlpuart1, (uint8_t*)command, strlen(command), 1000);
    HAL_UART_Transmit(&hlpuart1, (uint8_t*)"\r\n", 2, 1000);

    // 等待响应，TCP连接可能需要更长时间
    uint16_t received_len = GSM_ReceiveData(15000);  // 15秒超时
    if (received_len > 0) {
        GSM_PrintResponse(command, gsm_rx_buffer);

        // 检查多种可能的成功响应
        if (strstr(gsm_rx_buffer, "CONNECT OK") != NULL ||
            strstr(gsm_rx_buffer, "ALREADY CONNECT") != NULL ||
            strstr(gsm_rx_buffer, "OK") != NULL) {
            gsm_state = GSM_STATE_CONNECTED;
            return GSM_OK;
        } else if (strstr(gsm_rx_buffer, "ERROR") != NULL ||
                   strstr(gsm_rx_buffer, "FAIL") != NULL) {
            return GSM_ERROR;
        }
    }

    return GSM_TIMEOUT;
}

/**
 * @brief  关闭服务器连接
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_CloseServer(void)
{
    // 清空接收缓冲区
    memset(gsm_rx_buffer, 0, sizeof(gsm_rx_buffer));

    // 发送关闭指令
    HAL_UART_Transmit(&hlpuart1, (uint8_t*)"AT+CIPCLOSE", 11, 1000);
    HAL_UART_Transmit(&hlpuart1, (uint8_t*)"\r\n", 2, 1000);

    // 等待响应
    uint16_t received_len = GSM_ReceiveData(5000);
    if (received_len > 0) {
        GSM_PrintResponse("AT+CIPCLOSE", gsm_rx_buffer);
        if (strstr(gsm_rx_buffer, "CLOSE OK") != NULL ||
            strstr(gsm_rx_buffer, "OK") != NULL ||
            strstr(gsm_rx_buffer, "CLOSED") != NULL) {
            gsm_state = GSM_STATE_READY;
            return GSM_OK;
        }
    }

    // 即使没有收到确认，也将状态设为READY
    gsm_state = GSM_STATE_READY;
    return GSM_OK;
}

/**
 * @brief  设置非透传快发模式
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_SetQuickSend(void)
{
    return GSM_SendATCommand("AT+CIPQSEND=1", "OK", GSM_AT_TIMEOUT_MS);
}

/**
 * @brief  发送数据
 * @param  data: 要发送的数据
 * @param  length: 数据长度
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_SendData(const char* data, uint16_t length)
{
    if (gsm_state != GSM_STATE_CONNECTED) {
        printf("GSM not connected, cannot send data\r\n");
        return GSM_ERROR;
    }

    // 计算实际数据长度
    uint16_t actual_length = strlen(data);

    // 步骤1: 发送AT+CIPSEND=长度指令
    char command[32];
    sprintf(command, "AT+CIPSEND=%d", actual_length);

    // 清空接收缓冲区
    memset(gsm_rx_buffer, 0, sizeof(gsm_rx_buffer));

    // 发送CIPSEND指令
    HAL_UART_Transmit(&hlpuart1, (uint8_t*)command, strlen(command), 1000);
    HAL_UART_Transmit(&hlpuart1, (uint8_t*)"\r\n", 2, 1000);

    // 等待">"提示符
    uint16_t received_len = GSM_ReceiveData(5000);
    if (received_len > 0) {
        GSM_PrintResponse(command, gsm_rx_buffer);
        if (strstr(gsm_rx_buffer, ">") != NULL) {
            // 步骤2: 发送实际数据
            HAL_UART_Transmit(&hlpuart1, (uint8_t*)data, actual_length, 5000);

            // 步骤3: 等待发送完成确认
            memset(gsm_rx_buffer, 0, sizeof(gsm_rx_buffer));
            received_len = GSM_ReceiveData(10000);  // 等待10秒确认
            if (received_len > 0) {
                GSM_PrintResponse("DATA_SEND", gsm_rx_buffer);
                if (strstr(gsm_rx_buffer, "SEND OK") != NULL ||
                    strstr(gsm_rx_buffer, "ACCEPT") != NULL ||
                    strstr(gsm_rx_buffer, "+CIPSEND:") != NULL) {
                    return GSM_OK;
                }
            }
        }
    }

    return GSM_ERROR;
}

/**
 * @brief  获取模块信息
 * @param  info: 模块信息结构体指针
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_GetInfo(GSM_Info_t* info)
{
    GSM_Status_t status;

    // 获取模块型号
    status = GSM_GetModel(info->model);
    if (status != GSM_OK) return status;

    // 获取CCID号
    status = GSM_GetCCID(info->ccid);
    if (status != GSM_OK) return status;

    // 获取电压
    status = GSM_GetVoltage(&info->voltage);
    if (status != GSM_OK) return status;

    // 获取信号强度
    status = GSM_GetSignal(&info->signal);
    if (status != GSM_OK) return status;

    // 获取网络注册状态
    status = GSM_CheckNetwork(&info->network_reg);

    return status;
}

/**
 * @brief  获取当前状态
 * @retval GSM_State_t 当前状态
 */
GSM_State_t GSM_GetState(void)
{
    return gsm_state;
}

/**
 * @brief  带重试机制的AT指令发送
 * @param  command: AT指令
 * @param  expected_response: 期望的响应
 * @param  timeout: 超时时间(毫秒)
 * @param  retry_count: 重试次数
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_SendATCommandWithRetry(const char* command, const char* expected_response, uint32_t timeout, uint8_t retry_count)
{
    GSM_Status_t status;
    uint8_t attempts = 0;

    do {
        attempts++;

        status = GSM_SendATCommand(command, expected_response, timeout);

        if (status == GSM_OK) {
            GSM_PrintResponse(command, gsm_rx_buffer);
            return GSM_OK;
        } else {
            if (attempts <= retry_count) {
                HAL_Delay(1000);  // 重试前等待1秒
            }
        }
    } while (attempts <= retry_count);

    return status;
}

/**
 * @brief  发送AT指令并等待响应（轮询方式）
 * @param  command: AT指令
 * @param  expected_response: 期望的响应
 * @param  timeout: 超时时间(毫秒)
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_SendATCommand(const char* command, const char* expected_response, uint32_t timeout)
{
    // 清空接收缓冲区
    memset(gsm_rx_buffer, 0, sizeof(gsm_rx_buffer));

    // 发送AT指令
    if (strlen(command) > 0) {
        HAL_UART_Transmit(&hlpuart1, (uint8_t*)command, strlen(command), 1000);
        HAL_UART_Transmit(&hlpuart1, (uint8_t*)"\r\n", 2, 1000);
    }

    // 轮询接收响应
    uint16_t received_len = GSM_ReceiveData(timeout);
    if (received_len == 0) {
        // 超时处理
        if (strlen(command) > 0) {
            GSM_PrintError(command);
        }
        return GSM_TIMEOUT;
    }

    // 检查响应
    if (strstr(gsm_rx_buffer, expected_response) != NULL) {
        return GSM_OK;
    }
    if (strstr(gsm_rx_buffer, "ERROR") != NULL) {
        return GSM_ERROR;
    }

    return GSM_ERROR;
}

/**
 * @brief  轮询接收数据（恢复原始阻塞接收方式）
 * @param  timeout: 超时时间(毫秒)
 * @retval uint16_t 接收到的数据长度
 */
static uint16_t GSM_ReceiveData(uint32_t timeout)
{
    uint8_t rx_byte;
    uint16_t index = 0;
    uint32_t start_time = HAL_GetTick();
    uint32_t last_receive_time = start_time;
    uint32_t no_data_timeout = 500;  // 500ms无数据则认为接收完成

    while ((HAL_GetTick() - start_time) < timeout) {
        // 使用较短的超时时间进行阻塞接收，避免丢失数据
        if (HAL_UART_Receive(&hlpuart1, &rx_byte, 1, 50) == HAL_OK) {
            if (index < GSM_RX_BUFFER_SIZE - 1) {
                gsm_rx_buffer[index++] = rx_byte;
                gsm_rx_buffer[index] = '\0';
                last_receive_time = HAL_GetTick();

                // 检查是否是网络下发指令（以"ZL+"开头）
                if (index >= 3 && strncmp(gsm_rx_buffer, "ZL+", 3) == 0) {
                    // 继续接收直到换行符
                    if (rx_byte == '\n' || rx_byte == '\r') {
                        // 这是网络下发指令，处理它
                        GSM_ProcessNetworkCommand(gsm_rx_buffer, index);
                        // 清空缓冲区，不返回给AT指令处理
                        index = 0;
                        memset(gsm_rx_buffer, 0, sizeof(gsm_rx_buffer));
                        continue;
                    }
                }
            }
        } else {
            // 如果一段时间没有接收到数据，且已经有数据，则认为接收完成
            if (index > 0 && (HAL_GetTick() - last_receive_time) > no_data_timeout) {
                break;
            }
        }
    }

    return index;
}

/**
 * @brief  启动网络指令监听
 */
void GSM_StartNetworkCommandListener(void)
{
    printf("Network command listener ready\r\n");
}

/**
 * @brief  检查网络下发指令
 */
void GSM_CheckNetworkCommand(void)
{
    static char cmd_buffer[64];
    static uint16_t cmd_index = 0;
    uint8_t rx_byte;

    // 非阻塞检查是否有数据
    while (HAL_UART_Receive(&hlpuart1, &rx_byte, 1, 1) == HAL_OK) {
        cmd_buffer[cmd_index++] = rx_byte;

        // 如果是换行符，处理指令
        if (rx_byte == '\n' || rx_byte == '\r') {
            cmd_buffer[cmd_index] = '\0';

            // 检查是否是网络指令
            if (cmd_index >= 3 && strncmp(cmd_buffer, "ZL+", 3) == 0) {
                GSM_ProcessNetworkCommand(cmd_buffer, cmd_index);
            }

            cmd_index = 0;
            memset(cmd_buffer, 0, sizeof(cmd_buffer));
            break;  // 处理完一条指令后退出
        }

        // 防止缓冲区溢出
        if (cmd_index >= sizeof(cmd_buffer) - 1) {
            cmd_index = 0;
            memset(cmd_buffer, 0, sizeof(cmd_buffer));
        }
    }
}

/**
 * @brief  处理网络下发指令
 * @param  command: 指令字符串
 * @param  length: 指令长度
 */
void GSM_ProcessNetworkCommand(const char* command, uint16_t length)
{
    // 移除换行符
    char clean_command[64];
    uint16_t clean_length = 0;

    for (uint16_t i = 0; i < length && i < sizeof(clean_command) - 1; i++) {
        if (command[i] != '\r' && command[i] != '\n') {
            clean_command[clean_length++] = command[i];
        }
    }
    clean_command[clean_length] = '\0';

    if (clean_length > 0) {
        printf("Network command received: %s\r\n", clean_command);

        // 处理网络指令
        extern uint8_t network_cmd_new_data;
        extern char network_cmd_buffer[];
        extern uint16_t network_cmd_buffer_index;

        // 复制到网络指令缓冲区
        strncpy(network_cmd_buffer, clean_command, 32 - 1);
        network_cmd_buffer[31] = '\0';
        network_cmd_buffer_index = strlen(network_cmd_buffer);
        network_cmd_new_data = 1;
    }
}



/**
 * @brief  打印AT指令响应
 * @param  command: AT指令
 * @param  response: 响应内容
 */
static void GSM_PrintResponse(const char* command, const char* response)
{
    printf("AT Response [%s]: %s\r\n", command, response);
}

/**
 * @brief  打印错误信息
 * @param  command: 失败的AT指令
 */
static void GSM_PrintError(const char* command)
{
    printf("AT Command ERROR: %s\r\n", command);
}

/* USER CODE BEGIN 2 */

/* USER CODE END 2 */
